'use client'

import React, { useState, useRef, useCallback } from 'react'
import { useAppStore } from '@/lib/store'
import { X, Plus } from 'lucide-react'
import WorkArea from './WorkArea'
import AIAssistant from './AIAssistant'

const MainLayout: React.FC = () => {
  const { tabs } = useAppStore()
  const [leftPanelWidth, setLeftPanelWidth] = useState(54) // 默认54%主内容，46%AI助手
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // 处理拖拽开始
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  // 处理拖拽过程
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return

    const containerRect = containerRef.current.getBoundingClientRect()
    const newLeftWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100

    // 限制宽度范围：25% - 85%，提供更大的调节范围
    const clampedWidth = Math.max(25, Math.min(85, newLeftWidth))
    setLeftPanelWidth(clampedWidth)
  }, [isDragging])

  // 处理拖拽结束
  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // 添加全局鼠标事件监听
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  // 判断当前活跃标签页是否为空的新建标签页
  const activeTab = tabs.find(tab => tab.id === useAppStore.getState().activeTabId)
  const isEmptyNewTab = activeTab &&
    activeTab.sourceData === '' &&
    activeTab.originalContent === '' &&
    activeTab.aiNoteMarkdown === ''

  return (
    <div className="h-screen adaptive-light flex flex-col relative">
      {/* 背景装饰 */}
      <div className="bg-decoration">
        <div className="bg-circle"></div>
        <div className="bg-circle"></div>
        <div className="bg-circle"></div>
      </div>

      {/* 动态标签栏 - 只在有标签页时显示 */}
      {tabs.length > 0 && (
        <div className="glass-effect-strong border-b border-border/50 flex items-center px-6 shadow-sm flex-shrink-0">
          {/* Chrome风格标签页布局 */}
          <div className="flex items-center">
            {/* 标签页列表 - 紧密排列 */}
            <div className="flex items-center overflow-x-auto">
              {tabs.map((tab) => (
                <div
                  key={tab.id}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-t-xl cursor-pointer transition-all duration-200 flex-shrink-0 ${
                    tab.id === useAppStore.getState().activeTabId
                      ? 'glass-effect text-primary border-b-2 border-primary shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                  }`}
                  onClick={() => useAppStore.getState().setActiveTab(tab.id)}
                >
                  <span className="text-sm font-medium truncate max-w-32">
                    {tab.title}
                  </span>
                  {tab.isLoading && (
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  )}
                  {tab.aiAnalyzing && (
                    <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                  )}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      useAppStore.getState().removeTab(tab.id)
                    }}
                    className="p-1 hover:bg-gray-200/80 rounded-full transition-colors"
                  >
                    <X size={14} />
                  </button>
                </div>
              ))}
            </div>

            {/* Chrome风格新建标签页按钮 - 紧贴最右侧标签页 */}
            <button
              onClick={() => {
                useAppStore.getState().addTab({
                  title: '新标签页',
                  sourceType: 'text',
                  sourceData: '',
                  originalContent: '',
                  aiNoteMarkdown: '',
                  isLoading: false
                })
              }}
              className="ml-1 p-2 hover:bg-white/60 rounded-lg transition-all duration-200 flex-shrink-0"
              title="新建标签页"
            >
              <Plus size={14} className="text-gray-500" />
            </button>
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <div
        ref={containerRef}
        className="flex-1 flex overflow-hidden min-h-0 p-2"
      >
        {/* 主工作区 */}
        <div
          className="flex flex-col min-h-0 liquid-panel mr-2"
          style={{
            width: tabs.length > 0 && !isEmptyNewTab ? `${leftPanelWidth}%` : '100%',
            transition: isDragging ? 'none' : 'width 0.15s ease-out'
          }}
        >
          <WorkArea />
        </div>

        {/* 简化的可拖拽分隔条 - 减少干扰动画 */}
        {tabs.length > 0 && !isEmptyNewTab && (
          <div
            className={`group flex-shrink-0 relative cursor-col-resize transition-all duration-150 ${
              isDragging ? 'shadow-md' : ''
            }`}
            onMouseDown={handleMouseDown}
            style={{
              width: isDragging ? '6px' : '4px',
              margin: '0 2px'
            }}
          >
            {/* 拖拽手柄 */}
            <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 flex items-center justify-center">
              <div className={`w-0.5 h-12 bg-gradient-to-b from-blue-400 to-purple-500 rounded-full transition-all duration-150 ${
                isDragging ? 'h-16' : 'group-hover:h-16'
              }`}></div>
            </div>

            {/* 悬浮提示 */}
            <div className={`absolute top-1/2 left-full ml-2 transform -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap transition-opacity duration-200 pointer-events-none ${
              isDragging ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
            }`}>
              {Math.round(leftPanelWidth)}% / {Math.round(100 - leftPanelWidth)}%
            </div>
          </div>
        )}

        {/* AI助手面板 - 只在非空标签页时显示 */}
        {tabs.length > 0 && !isEmptyNewTab && (
          <div
            className="liquid-panel overflow-hidden flex flex-col"
            style={{
              width: `${100 - leftPanelWidth}%`,
              transition: isDragging ? 'none' : 'width 0.15s ease-out'
            }}
          >
            <AIAssistant />
          </div>
        )}
      </div>
    </div>
  )
}

export default MainLayout
