import { NextRequest, NextResponse } from 'next/server'
import { askQuestionWithContext, askQuestionWithContextAgent, askQuestionWithContextStreaming } from '@/lib/ai'

export async function POST(request: NextRequest) {
  try {
    const {
      message,
      context,
      chatHistory = [],
      mode = 'ask' // 默认为Ask模式
    } = await request.json()

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      )
    }

    if (!context || (!context.originalContent && !context.aiNote)) {
      return NextResponse.json(
        { error: 'Document context is required' },
        { status: 400 }
      )
    }

    // 构建文档上下文
    const documentContext = `
原文内容：
${context.originalContent || ''}

AI笔记：
${context.aiNote || ''}
    `.trim()

    // 根据模式调用不同的处理函数
    if (mode === 'agent') {
      // Agent模式：返回回答和结构化笔记修改建议
      const result = await askQuestionWithContextAgent(message, documentContext, chatHistory)
      return NextResponse.json(result)
    } else {
      // Ask模式：返回流式响应
      const stream = new ReadableStream({
        async start(controller) {
          try {
            const answer = await askQuestionWithContext(message, documentContext, chatHistory)

            // 模拟流式输出
            const words = answer.split('')
            for (let i = 0; i < words.length; i++) {
              const chunk = words[i]
              const data = JSON.stringify({
                content: chunk
              })
              controller.enqueue(new TextEncoder().encode(`data: ${data}\n\n`))

              // 添加小延迟以模拟流式效果
              if (i % 10 === 0) {
                await new Promise(resolve => setTimeout(resolve, 50))
              }
            }

            controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
            controller.close()
          } catch (error) {
            console.error('Streaming error:', error)
            controller.error(error)
          }
        }
      })

      return new NextResponse(stream, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      })
    }

  } catch (error) {
    console.error('Chat with document API error:', error)
    return NextResponse.json(
      { error: 'Failed to process chat request' },
      { status: 500 }
    )
  }
}